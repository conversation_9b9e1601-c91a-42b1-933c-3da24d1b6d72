package com.wexl.pallavi.preprimary.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.pallavi.preprimary.model.CompetencyTypes;
import com.wexl.pallavi.preprimary.model.PeerAssessmentTypes;
import java.util.List;
import lombok.Builder;

public record ProgressCardDto() {

  @Builder
  public record Request(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId,
      @JsonProperty("student_id") Long studentId,
      String gradeSlug,
      String gradeName,
      @JsonProperty("mother_phone_no") String motherPhoneNo,
      @JsonProperty("address") String address,
      @JsonProperty("thing_i_like") String thingsILike,
      @JsonProperty("i_live_in") String iLiveIn,
      @JsonProperty("my_friends_are") String myFriendsAre,
      @JsonProperty("my_favourite_colours_are") String myFavouriteColoursAre,
      @JsonProperty("my_favourite_foods") String myFavouriteFoods,
      @JsonProperty("my_favourite_games") String myFavouriteGames,
      @JsonProperty("my_favourite_animals") String myFavouriteAnimals,
      @JsonProperty("a_glimpse_of_myself") String aGlimpseOfMySelf,
      @JsonProperty("a_glimpse_of_myfamily") String aGlimpseOfMyFamily,
      @JsonProperty("learners_portfolio") String learnersPortFolio,
      @JsonProperty("flower") String flower,
      @JsonProperty("aim") String aim,
      @JsonProperty("subject") String subject,
      @JsonProperty("house") String house,
      @JsonProperty("term1_height") String term1Height,
      @JsonProperty("term2_height") String term2Height,
      @JsonProperty("term1_weight") String term1Weight,
      @JsonProperty("term2_weight") String term2Weight,
      @JsonProperty("term1_remarks") String term1Remarks,
      @JsonProperty("term2_remarks") String term2Remarks,
      @JsonProperty("age") Long age,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("dental") String dental,
      @JsonProperty("eye_sight_r") String eyeSightR,
      @JsonProperty("eye_sight_l") String eyeSightL,
      @JsonProperty("competencies") List<Competencies> competenciesList,
      @JsonProperty("self_assessments") List<SelfAssessment> selfAssessments,
      @JsonProperty("peer_assessments") List<PeerAssessment> peerAssessments,
      @JsonProperty("parents_feedback") List<ParentsFeedback> parentsFeedbacks,
      @JsonProperty("interested_activities") InterestedActivities interestedActivities,
      @JsonProperty("parent_facilitator") ParentFacilitator parentFacilitator,
      @JsonProperty("child_support") ChildSupport childSupport) {}

  @Builder
  public record Response(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("admission_no") String admissionNo,
      @JsonProperty("date_of_birth") Long dateOfBirth,
      @JsonProperty("class_teacher") String classTeacher,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("mother_name") String motherName,
      @JsonProperty("father_phone_no") String fatherPhoneNo,
      @JsonProperty("mother_phone_no") String motherPhoneNo,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("address") String address,
      @JsonProperty("thing_i_like") String thingsILike,
      @JsonProperty("i_live_in") String iLiveIn,
      @JsonProperty("my_friends_are") String myFriendsAre,
      @JsonProperty("my_favourite_colours_are") String myFavouriteColoursAre,
      @JsonProperty("my_favourite_foods") String myFavouriteFoods,
      @JsonProperty("flower") String flower,
      @JsonProperty("aim") String aim,
      @JsonProperty("subject") String subject,
      @JsonProperty("house") String house,
      @JsonProperty("my_favourite_games") String myFavouriteGames,
      @JsonProperty("my_favourite_animals") String myFavouriteAnimals,
      @JsonProperty("a_glimpse_of_myself") String aGlimpseOfMySelf,
      @JsonProperty("a_glimpse_of_myfamily") String aGlimpseOfMyFamily,
      @JsonProperty("a_glimpse_of_myself_path") String aGlimpseOfMySelfPath,
      @JsonProperty("a_glimpse_of_myfamily_path") String aGlimpseOfMyFamilyPath,
      @JsonProperty("learners_portfolio_path") String learnersPortFolioPath,
      @JsonProperty("learners_portfolio") String learnersPortFolio,
      @JsonProperty("term1_height") String term1Height,
      @JsonProperty("term2_height") String term2Height,
      @JsonProperty("term1_weight") String term1Weight,
      @JsonProperty("term2_weight") String term2Weight,
      @JsonProperty("age") Long age,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("dental") String dental,
      @JsonProperty("eye_sight_r") String eyeSightR,
      @JsonProperty("eye_sight_l") String eyeSightL,
      @JsonProperty("competencies") List<Competencies> competenciesList,
      @JsonProperty("self_assessments") List<SelfAssessment> selfAssessments,
      @JsonProperty("peer_assessments") List<PeerAssessment> peerAssessment,
      @JsonProperty("parents_feedback") List<ParentsFeedback> parentsFeedbacks,
      @JsonProperty("interested_activities") InterestedActivities interestedActivities,
      @JsonProperty("parent_facilitator") ParentFacilitator parentFacilitator,
      @JsonProperty("child_support") ChildSupport childSupport) {}

  @Builder
  public record Competencies(
      Long id,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("details") List<Skill> skills) {}

  @Builder
  public record Skill(
      @JsonProperty("skill_name") String skillName, List<Details> competencyDetails) {}

  @Builder
  public record Details(Long id, String name, CompetencyTypes term1, CompetencyTypes term2) {}

  @Builder
  public record SelfAssessment(Long id, String name, String term1, String term2) {}

  @Builder
  public record PeerAssessment(
      Long id, String name, PeerAssessmentTypes term1, PeerAssessmentTypes term2) {}

  @Builder
  public record ParentsFeedback(Long id, String name, String term1, String term2) {}

  @Builder
  public record ProgressCardResponse(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId) {}

  @Builder
  public record HolisticReportRequest(
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_uuid") String sectionUuid) {}
}
